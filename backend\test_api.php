<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Request;

$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        api: __DIR__.'/routes/api.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// 获取用户并创建token
$user = App\Models\User::where('username', 'sysadmin')->first();
if (!$user) {
    echo "User not found\n";
    exit(1);
}

// 创建token
$token = $user->createToken('test-token')->plainTextToken;
echo "Token created: " . substr($token, 0, 20) . "...\n";

// 模拟API请求
try {
    // 创建请求
    $request = Request::create('/api/organizations', 'GET', [
        'search' => '',
        'page' => 1,
        'per_page' => 20
    ]);
    
    // 设置认证头
    $request->headers->set('Authorization', 'Bearer ' . $token);
    $request->headers->set('Accept', 'application/json');
    
    // 设置当前用户
    auth()->setUser($user);
    
    // 调用控制器
    $controller = new App\Http\Controllers\Api\OrganizationController();
    $response = $controller->index($request);
    
    echo "API Response Status: " . $response->getStatusCode() . "\n";
    $data = json_decode($response->getContent(), true);
    
    if ($data) {
        echo "Response message: " . ($data['message'] ?? 'No message') . "\n";
        echo "Response code: " . ($data['code'] ?? 'No code') . "\n";
        
        if (isset($data['data'])) {
            if (isset($data['data']['data'])) {
                echo "Organizations count: " . count($data['data']['data']) . "\n";
                foreach ($data['data']['data'] as $org) {
                    echo "- " . $org['name'] . " (ID: " . $org['id'] . ")\n";
                }
            } else {
                echo "Data structure: " . json_encode(array_keys($data['data'])) . "\n";
            }
        }
    } else {
        echo "Response content: " . $response->getContent() . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
