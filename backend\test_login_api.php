<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Request;

$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        api: __DIR__.'/routes/api.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// 测试登录API
try {
    echo "Testing login API...\n";
    
    // 创建登录请求
    $loginRequest = Request::create('/api/auth/login', 'POST', [], [], [], [], json_encode([
        'username' => 'sysadmin',
        'password' => '123456'
    ]));

    $loginRequest->headers->set('Accept', 'application/json');
    $loginRequest->headers->set('Content-Type', 'application/json');
    
    // 调用登录控制器
    $authController = new App\Http\Controllers\Api\AuthController();
    $loginResponse = $authController->login($loginRequest);
    
    echo "Login Response Status: " . $loginResponse->getStatusCode() . "\n";
    $loginData = json_decode($loginResponse->getContent(), true);
    
    if ($loginData && isset($loginData['data']['token'])) {
        $token = $loginData['data']['token'];
        echo "Login successful! Token: " . substr($token, 0, 20) . "...\n";
        
        // 测试组织机构API
        echo "\nTesting organizations API...\n";
        
        $orgRequest = Request::create('/api/organizations', 'GET', [
            'search' => '',
            'page' => 1,
            'per_page' => 20
        ]);
        
        $orgRequest->headers->set('Authorization', 'Bearer ' . $token);
        $orgRequest->headers->set('Accept', 'application/json');
        
        // 设置当前用户
        $user = App\Models\User::where('username', 'sysadmin')->first();
        auth()->setUser($user);
        
        // 调用组织机构控制器
        $orgController = new App\Http\Controllers\Api\OrganizationController();
        $orgResponse = $orgController->index($orgRequest);
        
        echo "Organizations API Response Status: " . $orgResponse->getStatusCode() . "\n";
        $orgData = json_decode($orgResponse->getContent(), true);
        
        if ($orgData) {
            echo "Response message: " . ($orgData['message'] ?? 'No message') . "\n";
            echo "Response code: " . ($orgData['code'] ?? 'No code') . "\n";
            
            if (isset($orgData['data']['data'])) {
                echo "Organizations count: " . count($orgData['data']['data']) . "\n";
                foreach ($orgData['data']['data'] as $org) {
                    echo "- " . $org['name'] . " (ID: " . $org['id'] . ", Level: " . $org['level'] . ")\n";
                }
            }
        }
        
    } else {
        echo "Login failed!\n";
        echo "Response: " . $loginResponse->getContent() . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
