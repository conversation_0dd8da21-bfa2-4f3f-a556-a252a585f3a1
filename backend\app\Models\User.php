<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    protected $fillable = [
        'username',
        'email',
        'phone',
        'password',
        'real_name',
        'employee_id',
        'position',
        'department',
        'status',
        'last_login_at',
        'last_login_ip',
        'avatar',
        'gender',
        'birthday',
        'join_date',
        'remark'
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'status' => 'boolean',
        'last_login_at' => 'datetime',
        'birthday' => 'date',
        'join_date' => 'date',
        'email_verified_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    protected $attributes = [
        'status' => true,
        'gender' => 'other'
    ];

    /**
     * 组织机构关系（多对多）
     */
    public function organizations(): BelongsToMany
    {
        return $this->belongsToMany(Organization::class, 'user_organizations')
            ->withPivot(['is_primary', 'status'])
            ->withTimestamps();
    }

    /**
     * 主要组织机构关系
     */
    public function primaryOrganization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    /**
     * 角色关系（多对多）
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'role_user')
            ->withPivot(['organization_id', 'scope_type', 'effective_date', 'expiry_date', 'status', 'remarks', 'created_by'])
            ->withTimestamps();
    }

    /**
     * 权限关系（多对多，直接授权）
     */
    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'user_permissions')
            ->withPivot(['organization_id', 'granted_by', 'granted_at', 'status'])
            ->withTimestamps();
    }

    /**
     * 获取用户在指定组织下的角色
     */
    public function rolesInOrganization($organizationId)
    {
        return $this->roles()->wherePivot('organization_id', $organizationId);
    }

    /**
     * 获取用户的所有权限（包括角色权限和直接分配的权限）
     */
    public function getAllPermissions($organizationId = null)
    {
        $permissions = collect();

        // 获取角色权限
        $roles = $organizationId 
            ? $this->rolesInOrganization($organizationId)->get()
            : $this->roles;

        foreach ($roles as $role) {
            $rolePermissions = $role->permissions;
            $permissions = $permissions->merge($rolePermissions);
        }

        // 获取直接分配的权限
        $directPermissions = $organizationId
            ? $this->permissions()->wherePivot('organization_id', $organizationId)->get()
            : $this->permissions;

        $permissions = $permissions->merge($directPermissions);

        return $permissions->unique('id');
    }

    /**
     * 检查用户是否有指定权限
     */
    public function hasPermission($permission, $organizationId = null): bool
    {
        $permissionName = is_string($permission) ? $permission : $permission->name;
        
        $userPermissions = $this->getAllPermissions($organizationId);
        
        return $userPermissions->contains('name', $permissionName);
    }

    /**
     * 检查用户是否有任一指定权限
     */
    public function hasAnyPermission(array $permissions, $organizationId = null): bool
    {
        foreach ($permissions as $permission) {
            if ($this->hasPermission($permission, $organizationId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查用户是否有所有指定权限
     */
    public function hasAllPermissions(array $permissions, $organizationId = null): bool
    {
        foreach ($permissions as $permission) {
            if (!$this->hasPermission($permission, $organizationId)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查用户是否有指定角色
     */
    public function hasRole($role, $organizationId = null): bool
    {
        $roleName = is_string($role) ? $role : $role->name;
        
        $userRoles = $organizationId 
            ? $this->rolesInOrganization($organizationId)->get()
            : $this->roles;
            
        return $userRoles->contains('name', $roleName);
    }

    /**
     * 分配角色
     */
    public function assignRole($role, $organizationId = null, $assignedBy = null)
    {
        $roleId = is_object($role) ? $role->id : $role;
        
        $pivotData = [
            'organization_id' => $organizationId,
            'assigned_by' => $assignedBy,
            'assigned_at' => now(),
            'status' => true
        ];

        return $this->roles()->attach($roleId, $pivotData);
    }

    /**
     * 移除角色
     */
    public function removeRole($role, $organizationId = null)
    {
        $roleId = is_object($role) ? $role->id : $role;
        
        if ($organizationId) {
            return $this->roles()->wherePivot('organization_id', $organizationId)->detach($roleId);
        }
        
        return $this->roles()->detach($roleId);
    }

    /**
     * 分配权限
     */
    public function givePermission($permission, $organizationId = null, $grantedBy = null)
    {
        $permissionId = is_object($permission) ? $permission->id : $permission;
        
        $pivotData = [
            'organization_id' => $organizationId,
            'granted_by' => $grantedBy,
            'granted_at' => now(),
            'status' => true
        ];

        return $this->permissions()->attach($permissionId, $pivotData);
    }

    /**
     * 移除权限
     */
    public function revokePermission($permission, $organizationId = null)
    {
        $permissionId = is_object($permission) ? $permission->id : $permission;
        
        if ($organizationId) {
            return $this->permissions()->wherePivot('organization_id', $organizationId)->detach($permissionId);
        }
        
        return $this->permissions()->detach($permissionId);
    }

    /**
     * 获取用户的数据访问范围
     */
    public function getDataAccessScope()
    {
        $organizationIds = collect();
        $isSystemAdmin = false;

        // 获取用户所属的所有组织
        $organizations = $this->organizations;

        foreach ($organizations as $organization) {
            // 根据用户在组织中的角色确定访问范围
            $roles = $this->rolesInOrganization($organization->id)->get();

            foreach ($roles as $role) {
                // 检查是否是系统管理员
                if ($role->level == 1) {
                    $isSystemAdmin = true;
                    break 2; // 跳出两层循环
                }

                $roleScope = $this->calculateRoleScope($role, $organization);
                $organizationIds = $organizationIds->merge($roleScope);
            }
        }

        // 如果是系统管理员，返回全局访问权限
        if ($isSystemAdmin) {
            return [
                'type' => 'all',
                'organizations' => []
            ];
        }

        // 如果有特定组织权限，返回特定权限
        if ($organizationIds->isNotEmpty()) {
            return [
                'type' => 'specific',
                'organizations' => $organizationIds->unique()->values()->toArray()
            ];
        }

        // 默认无权限
        return [
            'type' => 'none',
            'organizations' => []
        ];
    }

    /**
     * 计算角色在组织中的数据访问范围
     */
    private function calculateRoleScope($role, $organization)
    {
        $scope = collect();
        
        // 根据角色级别确定访问范围
        switch ($role->level) {
            case 1: // 系统管理员 - 全局访问
                $scope = Organization::pluck('id');
                break;
                
            case 2: // 组织管理员 - 当前组织及其下级
                $scope = $organization->getAccessScope();
                break;
                
            case 3: // 部门管理员 - 当前部门及其下级
                $scope = $organization->getAccessScope();
                break;
                
            case 4: // 普通用户 - 当前组织
                $scope = collect([$organization->id]);
                break;
                
            case 5: // 受限用户 - 仅自己的数据
                $scope = collect(); // 需要在业务逻辑中进一步限制
                break;
        }
        
        return $scope;
    }

    /**
     * 获取用户显示名称
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->real_name ?: $this->username;
    }

    /**
     * 获取用户头像URL
     */
    public function getAvatarUrlAttribute(): string
    {
        return $this->avatar ?: '/images/default-avatar.png';
    }

    /**
     * 查询作用域：活跃用户
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', true);
    }

    /**
     * 查询作用域：指定组织的用户
     */
    public function scopeInOrganization(Builder $query, $organizationId): Builder
    {
        return $query->whereHas('organizations', function ($q) use ($organizationId) {
            $q->where('organization_id', $organizationId);
        });
    }

    /**
     * 查询作用域：有指定角色的用户
     */
    public function scopeWithRole(Builder $query, $roleName): Builder
    {
        return $query->whereHas('roles', function ($q) use ($roleName) {
            $q->where('name', $roleName);
        });
    }

    /**
     * 查询作用域：有指定权限的用户
     */
    public function scopeWithPermission(Builder $query, $permissionName): Builder
    {
        return $query->whereHas('permissions', function ($q) use ($permissionName) {
            $q->where('name', $permissionName);
        })->orWhereHas('roles.permissions', function ($q) use ($permissionName) {
            $q->where('name', $permissionName);
        });
    }

    /**
     * 更新最后登录信息
     */
    public function updateLastLogin($ip = null)
    {
        $this->update([
            'last_login_at' => now(),
            'last_login_ip' => $ip ?: request()->ip()
        ]);
    }

    /**
     * 检查用户是否可以访问指定组织的数据
     */
    public function canAccessOrganization($organizationId): bool
    {
        $accessScope = $this->getDataAccessScope();

        // 如果是全局权限，可以访问所有组织
        if ($accessScope['type'] === 'all') {
            return true;
        }

        // 如果是特定权限，检查是否在允许的组织列表中
        if ($accessScope['type'] === 'specific') {
            return in_array($organizationId, $accessScope['organizations']);
        }

        // 其他情况不允许访问
        return false;
    }

    /**
     * 获取用户可管理的角色级别
     */
    public function getManageableLevels(): array
    {
        $maxLevel = 5; // 默认最低级别

        // 获取用户所有角色中的最高级别
        foreach ($this->organizations as $organization) {
            $roles = $this->rolesInOrganization($organization->id)->get();
            foreach ($roles as $role) {
                if ($role->level < $maxLevel) {
                    $maxLevel = $role->level;
                }
            }
        }

        // 根据用户的最高角色级别确定可管理的级别
        switch ($maxLevel) {
            case 1: // 系统管理员
                return [2, 3, 4, 5]; // 可管理组织管理员及以下
            case 2: // 组织管理员
                return [3, 4, 5]; // 可管理部门管理员及以下
            case 3: // 部门管理员
                return [4, 5]; // 可管理普通用户及以下
            default:
                return []; // 无管理权限
        }
    }

    /**
     * 获取用户在组织中的最高权限级别
     */
    public function getHighestLevelInOrganization($organizationId): int
    {
        $roles = $this->rolesInOrganization($organizationId)->get();
        
        if ($roles->isEmpty()) {
            return 5; // 最低权限级别
        }
        
        return $roles->min('level');
    }

    public function getAuthIdentifierName()
    {
        return 'username';
    }



}