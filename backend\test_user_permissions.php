<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        api: __DIR__.'/routes/api.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// 测试用户权限数据
$user = App\Models\User::where('username', 'sysadmin')->first();

if ($user) {
    echo "=== 用户信息 ===\n";
    echo "用户名: " . $user->username . "\n";
    echo "用户ID: " . $user->id . "\n";
    
    echo "\n=== 用户角色 ===\n";
    $roles = $user->roles;
    echo "角色数量: " . $roles->count() . "\n";
    foreach ($roles as $role) {
        echo "- " . $role->name . " (" . $role->display_name . ", Level: " . $role->level . ")\n";
    }
    
    echo "\n=== 用户权限 ===\n";
    $permissions = $user->getAllPermissions();
    echo "权限数量: " . $permissions->count() . "\n";
    foreach ($permissions as $permission) {
        echo "- " . $permission->name . " (" . $permission->display_name . ")\n";
    }
    
    echo "\n=== 组织机构相关权限检查 ===\n";
    $orgPermissions = [
        'organization.view',
        'organization.create', 
        'organization.update',
        'organization.delete',
        'organization.index'
    ];
    
    foreach ($orgPermissions as $perm) {
        $hasPermission = $user->hasPermission($perm);
        echo "- " . $perm . ": " . ($hasPermission ? "✅ 有权限" : "❌ 无权限") . "\n";
    }
    
    echo "\n=== 数据库中的组织权限 ===\n";
    $dbPermissions = App\Models\Permission::where('name', 'like', 'organization%')->get();
    echo "数据库中组织权限数量: " . $dbPermissions->count() . "\n";
    foreach ($dbPermissions as $perm) {
        echo "- " . $perm->name . " (" . $perm->display_name . ")\n";
    }
    
} else {
    echo "用户 'sysadmin' 未找到\n";
}
