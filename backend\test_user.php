<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        api: __DIR__.'/routes/api.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// 测试用户数据
$user = App\Models\User::where('username', 'sysadmin')->first();

if ($user) {
    echo "User found: " . $user->username . "\n";
    echo "User ID: " . $user->id . "\n";
    echo "Organizations count: " . $user->organizations->count() . "\n";
    echo "Roles count: " . $user->roles->count() . "\n";
    
    try {
        $scope = $user->getDataAccessScope();
        echo "Access scope type: " . $scope['type'] . "\n";
        echo "Organizations in scope: " . count($scope['organizations']) . "\n";
    } catch (Exception $e) {
        echo "Error getting access scope: " . $e->getMessage() . "\n";
        echo "Stack trace: " . $e->getTraceAsString() . "\n";
    }
} else {
    echo "User 'sysadmin' not found\n";
    
    // 列出所有用户
    $users = App\Models\User::all();
    echo "Available users:\n";
    foreach ($users as $u) {
        echo "- " . $u->username . " (ID: " . $u->id . ")\n";
    }
}

// 检查组织数据
$orgs = App\Models\Organization::all();
echo "\nOrganizations count: " . $orgs->count() . "\n";
foreach ($orgs->take(5) as $org) {
    echo "- " . $org->name . " (ID: " . $org->id . ")\n";
}

// 检查角色数据
$roles = App\Models\Role::all();
echo "\nRoles count: " . $roles->count() . "\n";
foreach ($roles->take(5) as $role) {
    echo "- " . $role->name . " (Level: " . $role->level . ")\n";
}
